import React, { createContext, useContext, useMemo } from "react";

const SaasConfigContext = createContext();

export const useSaasConfigContext = () => {
  const context = useContext(SaasConfigContext);
  if (!context) {
    throw new Error(
      "useSaasConfigContext must be used within a SaasConfigProvider"
    );
  }
  return context;
};

export function SaasConfigProvider({ children, saasConfig }) {
  const value = useMemo(
    () => ({
      saasConfig,
      isSaaS: saasConfig?.isSaaS || false,
      saasHostToken: saasConfig?.hostToken || null,
      saasMeetingId: saasConfig?.meetingId || null,
      saasMeetingConfig: saasConfig?.meetingConfig || null,
      saasMeetingFeatures: saasConfig?.meetingFeatures || null,
    }),
    [saasConfig]
  );

  return (
    <SaasConfigContext.Provider value={value}>
      {children}
    </SaasConfigContext.Provider>
  );
}
