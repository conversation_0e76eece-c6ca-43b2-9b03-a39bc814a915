import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../../API/axios";
import { Endpoints } from "../routes/saasRoutes";

export const SaasService = {
  extendMeeting: async (extended, id, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.extend_meeting.method,
        endpoint: Endpoints.extend_meeting.url,
        payload: {
          is_extend_time: extended,
          meeting_uid: id,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error extending meeting", {
          response,
          payload: {
            is_extend_time: extended,
            meeting_uid: id,
          },
          endpoint: Endpoints.extend_meeting.url,
          // user,
        });
      } else {
        datadogLogs.logger.info("Success extending meeting", {
          response,
          payload: {
            is_extend_time: extended,
            meeting_uid: id,
          },
          endpoint: Endpoints.extend_meeting.url,
          // user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getParticipantConsentList: async (meetingId, sessionId) => {
    try {
      const response = await APIrequest({
        method: Endpoints.getParticipantConsentList(meetingId, sessionId)
          .method,
        endpoint: Endpoints.getParticipantConsentList(meetingId, sessionId).url,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting participant consent list", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
          },
          endpoint: Endpoints.getParticipantConsentList(meetingId, sessionId)
            .url,
        });
      } else {
        datadogLogs.logger.info("Success getting participant consent list", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
          },
          endpoint: Endpoints.getParticipantConsentList(meetingId, sessionId)
            .url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  updateParticipantConsent: async (
    meetingId,
    sessionId,
    user,
    attendenceId,
    participantConsentStatus
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.updateParticipantConsent.method,
        endpoint: Endpoints.updateParticipantConsent.url,
        payload: {
          meeting_uid: meetingId,
          session_id: sessionId,
          is_accepted: participantConsentStatus,
          attendance_id: attendenceId,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error updating participant consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            is_accepted: participantConsentStatus,
            attendence_id: attendenceId,
          },
          endpoint: Endpoints.updateParticipantConsent.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success updating participant consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            is_accepted: participantConsentStatus,
            attendance_id: attendenceId,
          },
          endpoint: Endpoints.updateParticipantConsent.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  startRecordingConsent: async (
    meetingId,
    sessionId,
    attendanceId,
    user,
    meetingConsentStatus,
    token = null
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.startRecordingConsent.method,
        endpoint: Endpoints.startRecordingConsent.url,
        payload: {
          meeting_uid: meetingId,
          session_id: sessionId,
          meeting_consent_start: meetingConsentStatus,
          attendance_id: attendanceId,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting recording consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            meeting_consent_start: meetingConsentStatus,
          },
          endpoint: Endpoints.startRecordingConsent.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting recording consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            meeting_consent_start: meetingConsentStatus,
          },
          endpoint: Endpoints.startRecordingConsent.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  // recording consent services
  getMeetingSession: async (meetingId, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.getMeetingSession(meetingId).method,
        endpoint: Endpoints.getMeetingSession(meetingId).url,
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting meeting session", {
          response,
          payload: { meeting_uid: meetingId },
        });
      } else {
        datadogLogs.logger.info("Success getting meeting session", {
          response,
          payload: { meeting_uid: meetingId },
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  uploadScreenshot: async (formData, headers, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.uploadScreenshot.method,
        endpoint: Endpoints.uploadScreenshot.url,
        payload: formData,
        token,
        formHeaders: headers,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error uploading screenshot", {
          response,
          payload: formData,
        });
      } else {
        datadogLogs.logger.info("Success uploading screenshot", {
          response,
          payload: formData,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
