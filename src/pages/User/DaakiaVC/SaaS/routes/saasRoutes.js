export const Endpoints = {
  extend_meeting: {
    url: "rtc/meeting/time/extend",
    method:"POST"
  },
  getParticipantConsentList: (meetingId, sessionId) => ({
    url: `/rtc/meeting/participant/consentList?meeting_uid=${meetingId}&session_id=${sessionId}`,
    method: "GET",
  }),
  getMeetingSession: (meetingId) => ({
    url: `/rtc/meeting/session/detail?meeting_uid=${meetingId}`,
    method: "GET",
  }),
  startRecordingConsent: {
    url: `/rtc/meeting/startRecording/consent`,
    method: "POST",
  },
  updateParticipantConsent:{
    url: `/rtc/meeting/updateRecording/consentStatus`,
    method: "PUT",
  },
  uploadScreenshot: {
    url: "/rtc/meeting/upload/screenshot",
    method: "POST",
  },
};