// import { SaasService } from "../services/saasServices";

// export const extendMeeting = async (extend = true, room_uid, token = null) => {
//   try {
//     const response = await SaasService.extendMeeting(
//       extend,
//       room_uid,
//       token
//     );
//     return response;
//   } catch (error) {
//     console.error("Error extending meeting:", error);
//     throw error;
//   }
// };
