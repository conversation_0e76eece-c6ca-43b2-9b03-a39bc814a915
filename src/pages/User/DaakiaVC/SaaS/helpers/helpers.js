import { useSaasConfigContext } from "../../context/SaasConfigContext";

// Context-aware hook versions (use these in components)
export const useSaasHelpers = () => {
  const {
    saasConfig,
    isSaaS,
    saasHostToken,
    saasMeetingFeatures,
    saasMeetingId,
  } = useSaasConfigContext();

  // Define functions that can reference each other
  const saasBrandingEnabled = () => {
    if (!isSaaS) return false;
    return (
      isSaaS &&
      saasMeetingFeatures?.configurations &&
      saasMeetingFeatures?.configurations?.branding_enabled === 1
    );
  };

  const saasMeetingConfigurations = () => {
    if (!saasBrandingEnabled()) return null;
    return saasMeetingFeatures?.configurations;
  };

  return {
    isSaaS,
    saasHostToken,
    saasMeetingFeatures,
    saasConfig,
    saasMeetingId,
    saasBrandingEnabled,
    saasMeetingConfigurations,
  };
};
