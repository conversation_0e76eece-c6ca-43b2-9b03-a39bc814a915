import { Suspense, useEffect, useState } from "react";
import { useRoutes } from "react-router-dom";
import { He<PERSON><PERSON><PERSON>rovider } from "react-helmet-async";
import { Spinner } from "react-bootstrap";
import { pdfjs } from 'react-pdf';
import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";
import { routes } from "./routes";
import { DATA_DOG_CONFIG } from "./utils/constants";
import "./App.css"
import "antd/dist/antd.min.css";

// pdfjs.GlobalWorkerOptions.workerSrc = new URL(
// 	'pdfjs-dist/build/pdf.worker.min.mjs',
// 	import.meta.url,
// ).toString();

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

function RouteLayout({ path }) {
  const element = useRoutes(path);
  return element;
}

function App() {
  const [isDataDogInitialized, setIsDataDogInitialized] = useState(false);

  // Initialize DataDog once when the app starts
  useEffect(() => {
    if (!isDataDogInitialized) {
      datadogLogs.init({
        clientToken: DATA_DOG_CONFIG.TOKEN,
        site: DATA_DOG_CONFIG.SITE,
        service: DATA_DOG_CONFIG.LOG_SERVICE,
        env: DATA_DOG_CONFIG.ENV,
        // version: __COMMIT_HASH__,
        forwardErrorsToLogs: false,
        sessionSampleRate: 100,
      });
      datadogRum.init({
        applicationId: DATA_DOG_CONFIG.RUM_APPLICATION_ID,
        clientToken: DATA_DOG_CONFIG.RUM_TOKEN,
        // `site` refers to the Datadog site parameter of your organization
        // see https://docs.datadoghq.com/getting_started/site/
        site: DATA_DOG_CONFIG.SITE,
        service: DATA_DOG_CONFIG.RUM_SERVICE,
        env: DATA_DOG_CONFIG.ENV,
        // Specify a version number to identify the deployed version of your application in Datadog
        // version: '1.0.0',
        sessionSampleRate: 100,
        sessionReplaySampleRate: 100,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: "mask-user-input",
      });

      setIsDataDogInitialized(true);
    }
  }, [isDataDogInitialized]);

  return (
    <>
      <HelmetProvider>
        <Suspense fallback={<div className="d-flex align-items-center justify-content-center h-100 mainLoader"><Spinner variant="light" animation="border" role="status"/></div>}>
          <RouteLayout path={routes()} />
        </Suspense>
      </HelmetProvider>
    </>
  );
}

export default App;
