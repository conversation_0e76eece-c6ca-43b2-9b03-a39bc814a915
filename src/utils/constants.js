export const OS_PLATFORM = Object.freeze({
  WINDOWS: "Windows",
  MAC: "Mac",
  LINUX: "Linux",
  UNKNOWN: "Unknown",
});

export const DESKTOP_DOWNLOAD_LINK = Object.freeze({
  WINDOWS: process.env.REACT_APP_DESKTOP_APP_DOWNLOAD_LINK_WINDOWS,
  MAC: process.env.REACT_APP_DESKTOP_APP_DOWNLOAD_LINK_MAC,
});


export const ANALYSIS_STATUS = Object.freeze({
  QUEUED: 'queued',
  TRANSCRIPTION_INPROGRESS: 'transcription_inprogress',
  TRANSCRIPTION_FAILED: 'transcription_failed',
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'unavailable',
  ANALYSIS_INPROGRESS: 'analysis_inprogress',
  ANALYSIS_FAILED: 'analysis_failed',
  COMPLETED: 'completed'
});

export const DATA_DOG_CONFIG = Object.freeze({
  TOKEN: process.env.REACT_APP_DATA_DOG_TOKEN,
  SITE: process.env.REACT_APP_DATADOG_SITE,
  LOG_SERVICE: process.env.REACT_APP_DATADOG_LOG_SERVICE,
  RUM_SERVICE: process.env.REACT_APP_DATADOG_RUM_SERVICE,
  ENV: process.env.REACT_APP_LOG_ENV,
  RUM_TOKEN: process.env.REACT_APP_DATA_DOG_RUM_TOKEN,
  RUM_APPLICATION_ID: process.env.REACT_APP_DATA_DOG_RUM_APPLICATION_ID,
});